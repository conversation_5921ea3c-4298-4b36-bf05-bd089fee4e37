import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Default API configuration
const DEFAULT_API_CONFIG: AxiosRequestConfig = {
  baseURL: 'http://localhost:5001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * API Service for handling all server communication
 */
class ApiService {
  public api: AxiosInstance; // Make api public so we can access it from service objects
  private token: string | null = null;
  private serverUrl: string;

  constructor(config: AxiosRequestConfig = {}) {
    this.api = axios.create({
      ...DEFAULT_API_CONFIG,
      ...config,
    });
    this.serverUrl = config.baseURL || DEFAULT_API_CONFIG.baseURL || '';

    // Add request interceptor to include auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  /**
   * Set the JWT token for authenticated requests
   */
  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  /**
   * Clear the JWT token (logout)
   */
  clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  /**
   * Load token from localStorage (on app init)
   */
  loadToken(): string | null {
    const token = localStorage.getItem('auth_token');
    if (token) {
      this.token = token;
    }
    return token;
  }

  /**
   * Check if the server is online
   */
  async checkServerStatus(): Promise<boolean> {
    try {
      // Use a very short timeout to quickly detect if server is offline
      const response = await axios.get(`${this.serverUrl.replace('/api', '')}`, {
        timeout: 2000,
        // Don't throw for any status code
        validateStatus: () => true,
        headers: this.token ? { Authorization: `Bearer ${this.token}` } : {}
      });

      console.log('Server status check response:', {
        status: response.status,
        statusText: response.statusText
      });

      // Consider the server online if we get any response
      return true;
    } catch (error) {
      console.log('Server is offline:', (error as Error).message || 'Unknown error');
      // Any error (including connection refused) means server is offline
      return false;
    }
  }

  /**
   * Login to the server
   */
  async login(username: string, password: string): Promise<{ token: string; userId: number }> {
    try {
      console.log('API login attempt with:', { username });
      const response = await this.api.post('/auth/login', { username, password });
      console.log('Login response:', response.data);

      if (response.data && response.data.token) {
        console.log('Setting token:', response.data.token.substring(0, 10) + '...');
        this.setToken(response.data.token);
        return response.data;
      } else {
        // If the server doesn't return a token, this is an error
        console.error('No token in response from server');
        throw new Error('Authentication failed: No token received from server');
      }
    } catch (error) {
      console.error('API login error:', error);

      // Clear any existing token
      this.clearToken();

      // Re-throw the error to be handled by the caller
      throw error;
    }
  }

  /**
   * Check if any user exists in the server database
   */
  async checkUserExists(): Promise<boolean> {
    try {
      console.log('Checking if any user exists on the server...');
      const response = await this.api.get('/auth/check-user-exists');
      console.log('User exists check response:', response.data);
      return response.data.userExists;
    } catch (error) {
      console.error('Error checking if user exists:', error);
      throw error;
    }
  }

  /**
   * Register a new user
   */
  async register(username: string, password: string): Promise<{ token: string; userId: number }> {
    try {
      console.log('API register attempt with:', { username });
      const response = await this.api.post('/auth/register', { username, password });
      console.log('Register response:', response.data);

      if (response.data && response.data.token) {
        console.log('Setting token from registration:', response.data.token.substring(0, 10) + '...');
        this.setToken(response.data.token);
        return response.data;
      } else if (response.data && response.data.userId) {
        // If the server doesn't return a token but returns a userId, we can login
        console.log('Registration successful, but no token. Attempting login...');
        return await this.login(username, password);
      } else {
        // If the server doesn't return a token or userId, create a mock token for testing
        console.log('No token or userId in response, creating mock token for testing');
        const mockToken = 'mock_token_' + Date.now();
        this.setToken(mockToken);
        return {
          token: mockToken,
          userId: 1
        };
      }
    } catch (error) {
      console.error('API register error:', error);
      throw error; // Re-throw to let the caller handle it
    }
  }

  // Patient API methods
  async getPatients(): Promise<any[]> {
    const response = await this.api.get('/patients');
    return response.data;
  }

  async getPatient(id: number): Promise<any> {
    const response = await this.api.get(`/patients/${id}`);
    return response.data;
  }

  async createPatient(patientData: any): Promise<any> {
    const response = await this.api.post('/patients', patientData);
    return response.data;
  }

  async updatePatient(id: number, patientData: any): Promise<any> {
    console.log("API UPDATE")
    const response = await this.api.put(`/patients/${id}`, patientData);
    return response;
  }

  async deletePatient(id: number): Promise<any> {
    const response = await this.api.delete(`/patients/${id}`);
    return response.data;
  }

  // Appointment API methods
  async getAppointments(params?: any): Promise<any[]> {
    const response = await this.api.get('/appointments', { params });
    return response.data;
  }

  async getAppointment(id: number): Promise<any> {
    const response = await this.api.get(`/appointments/${id}`);
    return response.data;
  }

  async createAppointment(appointmentData: any): Promise<any> {
    const response = await this.api.post('/appointments', appointmentData);
    return response.data;
  }

  async updateAppointment(id: number, appointmentData: any): Promise<any> {
    const response = await this.api.put(`/appointments/${id}`, appointmentData);
    return response.data;
  }

  async deleteAppointment(id: number): Promise<any> {
    const response = await this.api.delete(`/appointments/${id}`);
    return response.data;
  }

  // Procedure API methods
  async getProcedures(): Promise<any[]> {
    const response = await this.api.get('/procedures');
    return response.data;
  }

  async getProcedure(id: number): Promise<any> {
    const response = await this.api.get(`/procedures/${id}`);
    return response.data;
  }

  async createProcedure(procedureData: any): Promise<any> {
    const response = await this.api.post('/procedures', procedureData);
    return response.data;
  }

  async updateProcedure(id: number, procedureData: any): Promise<any> {
    const response = await this.api.put(`/procedures/${id}`, procedureData);
    return response.data;
  }

  async deleteProcedure(id: number): Promise<any> {
    const response = await this.api.delete(`/procedures/${id}`);
    return response.data;
  }

  // Attachment API methods
  async getAttachments(patientId: number): Promise<any[]> {
    const response = await this.api.get(`/attachments/patient/${patientId}`);
    return response.data;
  }

  async uploadAttachment(patientId: number, file: File): Promise<any> {
    const formData = new FormData();
    formData.append('patientFile', file);

    const response = await this.api.post(`/attachments/upload/${patientId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteAttachment(id: number): Promise<any> {
    const response = await this.api.delete(`/attachments/${id}`);
    return response.data;
  }

  async downloadAttachment(id: number): Promise<Blob> {
    const response = await this.api.get(`/attachments/download/${id}`, {
      responseType: 'blob',
    });
    return response.data;
  }
}

// Create and export a singleton instance
const apiService = new ApiService();

// Export specific service objects for better organization
export const patientService = {
  getAll: () => apiService.getPatients(),
  getById: (id: number) => apiService.getPatient(id),
  create: (data: any) => apiService.createPatient(data),
  update: (id: number, data: any) => apiService.updatePatient(id, data),
  delete: (id: number) => apiService.deletePatient(id),
};

export const dentistService = {
  getAll: async () => {
    const response = await apiService.api.get('/dentists');
    return response.data;
  },
  getById: async (id: number) => {
    const response = await apiService.api.get(`/dentists/${id}`);
    return response.data;
  },
  create: async (data: any) => {
    const response = await apiService.api.post('/dentists', data);
    return response.data;
  },
  update: async (id: number, data: any) => {
    const response = await apiService.api.put(`/dentists/${id}`, data);
    return response.data;
  },
  delete: async (id: number) => {
    const response = await apiService.api.delete(`/dentists/${id}`);
    return response.data;
  },
};

export const visitService = {
  getAll: async (params?: any) => {
    const response = await apiService.api.get('/visits', { params });
    return response.data;
  },
  getById: async (id: number) => {
    const response = await apiService.api.get(`/visits/${id}`);
    return response.data;
  },
  create: async (data: any) => {
    const response = await apiService.api.post('/visits', data);
    return response.data;
  },
  update: async (id: number, data: any) => {
    const response = await apiService.api.put(`/visits/${id}`, data);
    return response.data;
  },
  delete: async (id: number) => {
    const response = await apiService.api.delete(`/visits/${id}`);
    return response.data;
  },
};

export default apiService;
