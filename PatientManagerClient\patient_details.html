<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden"
      style="--select-button-svg: url('data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2724px%27 height=%2724px%27 fill=%27rgb(78,122,151)%27 viewBox=%270 0 256 256%27%3e%3cpath d=%27M181.66,170.34a8,8,0,0,1,0,11.32l-48,48a8,8,0,0,1-11.32,0l-48-48a8,8,0,0,1,11.32-11.32L128,212.69l42.34-42.35A8,8,0,0,1,181.66,170.34Zm-96-84.68L128,43.31l42.34,42.35a8,8,0,0,0,11.32-11.32l-48-48a8,8,0,0,0-11.32,0l-48,48A8,8,0,0,0,85.66,85.66Z%27%3e%3c/path%3e%3c/svg%3e'); font-family: Inter, &quot;Noto Sans&quot;, sans-serif;"
    >
      <div class="layout-container flex h-full grow flex-col">
        <div class="gap-1 px-6 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col w-80">
            <div class="flex p-4 @container">
              <div class="flex w-full flex-col gap-4 items-center">
                <div class="flex gap-4 flex-col items-center">
                  <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32"
                    style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCAyK7GdHM8BhTgTZy1oN-kl0HB80LOd6YxHCo7MJ69AskYhLRQxiJ1JzJ2dnEaoseYmXz6Spj_7GuU1pHICEve-GOPVClLC2RrgUkSzyJG8Y3SfPJW97zSNgvVJ9v-RAWossHjKSQaUiFDQ390zdOfN5Q6d4BialbrPCI0X8uGLB2IRgsMkLdkCq3E9-jQvNn0xM1IDcVoh3V5hW_sZkGLBvjbC7joKdU2-mhB7tYtyMOWwNfJyP1Y8eaWuDIW7GAbK-5qqLzDaw");'
                  ></div>
                  <div class="flex flex-col items-center justify-center justify-center">
                    <p class="text-[#0e161b] text-[22px] font-bold leading-tight tracking-[-0.015em] text-center">Emily Carter</p>
                    <p class="text-[#4e7a97] text-base font-normal leading-normal text-center">Patient ID: 123456</p>
                  </div>
                </div>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Contact Information</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Phone</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">(*************</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Email</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal"><EMAIL></p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Address</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">123 Main St, Anytown, USA</p>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Medical History</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Allergies</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Penicillin</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Medications</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">None</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Conditions</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">None</p>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Billing Information</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Insurance</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Acme Health</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Balance</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">$0.00</p>
              </div>
            </div>
            <div class="flex px-4 py-3">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 flex-1 bg-[#e7eef3] text-[#0e161b] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Edit Patient Info</span>
              </button>
            </div>
          </div>
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <div class="flex min-w-72 flex-col gap-3">
                <p class="text-[#0e161b] tracking-light text-[32px] font-bold leading-tight">Patient Details</p>
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Manage patient information and history</p>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Personal Information</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">First Name</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Emily</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Last Name</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Carter</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">National ID</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">90010112345</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Date of Birth</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">1990-01-01</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Address</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">123 Main St, Anytown, USA</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Occupation</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Teacher</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Gender</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Female</p>
              </div>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#d0dee7] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#1993e5] text-[#0e161b] pb-[13px] pt-4" href="#">
                  <p class="text-[#0e161b] text-sm font-bold leading-normal tracking-[0.015em]">Appointments</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#4e7a97] pb-[13px] pt-4" href="#">
                  <p class="text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">Visit Notes</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#4e7a97] pb-[13px] pt-4" href="#">
                  <p class="text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">Billing</p>
                </a>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Upcoming Appointments</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d0dee7] bg-slate-50">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-slate-50">
                      <th class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-120 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Date</th>
                      <th class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-240 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Time</th>
                      <th class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-360 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Type</th>
                      <th class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-480 px-4 py-3 text-left text-[#0e161b] w-60 text-sm font-medium leading-normal">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2024-07-15
                      </td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">10:00 AM</td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Cleaning</td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Scheduled</span>
                        </button>
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2024-08-20
                      </td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">2:00 PM</td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Checkup</td>
                      <td class="table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Scheduled</span>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-120{display: none;}}
                @container(max-width:240px){.table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-240{display: none;}}
                @container(max-width:360px){.table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-360{display: none;}}
                @container(max-width:480px){.table-2f418ff8-3b28-4889-98cf-f78c2ed50c18-column-480{display: none;}}
              </style>
            </div>
            <div class="flex px-4 py-3 justify-end">
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#1993e5] text-slate-50 text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span class="truncate">Schedule Appointment</span>
              </button>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Past Appointments</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d0dee7] bg-slate-50">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-slate-50">
                      <th class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-120 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Date</th>
                      <th class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-240 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Time</th>
                      <th class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-360 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Type</th>
                      <th class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-480 px-4 py-3 text-left text-[#0e161b] w-60 text-sm font-medium leading-normal">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2024-01-10
                      </td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">11:00 AM</td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Filling</td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Completed</span>
                        </button>
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2023-09-05
                      </td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">9:00 AM</td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Cleaning</td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Completed</span>
                        </button>
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2023-05-15
                      </td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">1:00 PM</td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Consultation
                      </td>
                      <td class="table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Completed</span>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-120{display: none;}}
                @container(max-width:240px){.table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-240{display: none;}}
                @container(max-width:360px){.table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-360{display: none;}}
                @container(max-width:480px){.table-3350a6ff-dc9e-4535-8aaa-6a2319ded728-column-480{display: none;}}
              </style>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Visit History</h3>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d0dee7] bg-slate-50">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-slate-50">
                      <th class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-120 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Date</th>
                      <th class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-240 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Reason</th>
                      <th class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-360 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Dentist</th>
                      <th class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-480 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2024-01-10
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Filling</td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Harper
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Filled cavity in tooth #18
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2023-09-05
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Cleaning</td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Harper
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Routine cleaning
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        2023-05-15
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Consultation
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Harper
                      </td>
                      <td class="table-c789658f-e5bd-4192-b816-bde136cf952f-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Initial consultation
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-c789658f-e5bd-4192-b816-bde136cf952f-column-120{display: none;}}
                @container(max-width:240px){.table-c789658f-e5bd-4192-b816-bde136cf952f-column-240{display: none;}}
                @container(max-width:360px){.table-c789658f-e5bd-4192-b816-bde136cf952f-column-360{display: none;}}
                @container(max-width:480px){.table-c789658f-e5bd-4192-b816-bde136cf952f-column-480{display: none;}}
              </style>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Treatment Details</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Last Treatment</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Filling on 2024-01-10</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Next Treatment</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Cleaning on 2024-07-15</p>
              </div>
            </div>
            <h3 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Oral Health Status</h3>
            <div class="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Mucous Membrane Condition</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Healthy</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Gum Condition</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Healthy</p>
              </div>
              <div class="col-span-2 grid grid-cols-subgrid border-t border-t-[#d0dee7] py-5">
                <p class="text-[#4e7a97] text-sm font-normal leading-normal">Hygiene Status</p>
                <p class="text-[#0e161b] text-sm font-normal leading-normal">Good</p>
              </div>
            </div>
            <div class="p-4">
              <div
                class="bg-cover bg-center flex flex-col items-stretch justify-end rounded-xl pt-[132px]"
                style='background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuBUWfOLIyIYMV2bC2o0iAyStSnyWR1DG8XIjcVI-ZDu8rvDRdhR3R10D_SsWm9Dv0rDgGResyv-u7iCcwr1y56JZKjIWq_wuWuUkkAlPjqh57fB2E6UV4iiH9mb-XCAm1JXgA_07fjNQf3kCAR8yL7fRAa8Jd8sW93OXV-IjJKr1DFycUQ8i8cwScWKERlS0aqs2kpP_vZ7SEvMr8wAEZNLDlCxhKffYazKnCIk5mupas9szC9IOWCZIJZAVB-l31d2Dys0eJ7b8w");'
              >
                <div class="flex w-full items-end justify-between gap-4 p-4">
                  <div class="flex max-w-[440px] flex-1 flex-col gap-1">
                    <p class="text-white tracking-light text-2xl font-bold leading-tight max-w-[440px]">Interactive Tooth Chart</p>
                    <p class="text-white text-base font-medium leading-normal">Click on a tooth to update its condition.</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
              <label class="flex flex-col min-w-40 flex-1">
                <select
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0e161b] focus:outline-0 focus:ring-0 border border-[#d0dee7] bg-slate-50 focus:border-[#d0dee7] h-14 bg-[image:--select-button-svg] placeholder:text-[#4e7a97] p-[15px] text-base font-normal leading-normal"
                >
                  <option value="one">Add Procedure</option>
                  <option value="two">two</option>
                  <option value="three">three</option>
                </select>
              </label>
            </div>
            <p class="text-[#0e161b] text-base font-normal leading-normal pb-3 pt-1 px-4">Select a procedure from the dropdown to add it to the patient's file.</p>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
