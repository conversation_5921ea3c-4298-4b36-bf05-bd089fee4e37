require('dotenv').config();
const sqlite3 = require('@journeyapps/sqlcipher').verbose();
const fs = require('fs');
const path = require('path');

const ENCRYPTED_DB_PATH = process.env.DB_PATH || path.join(__dirname, 'database/patientmanager_server.db');
const UNENCRYPTED_DB_PATH = path.join(__dirname, 'database/patientmanager_unencrypted.db');
const DB_ENCRYPTION_KEY = process.env.DB_ENCRYPTION_KEY;

console.log('Creating unencrypted copy of database for testing purposes...');
console.log('⚠️  WARNING: This creates an unencrypted copy - delete it after testing!');

// Remove existing unencrypted file if it exists
if (fs.existsSync(UNENCRYPTED_DB_PATH)) {
    fs.unlinkSync(UNENCRYPTED_DB_PATH);
    console.log('Removed existing unencrypted database file');
}

// Open encrypted database
const encryptedDb = new sqlite3.Database(ENCRYPTED_DB_PATH, (err) => {
    if (err) {
        console.error('Failed to open encrypted database:', err.message);
        process.exit(1);
    }
    console.log('Connected to encrypted database');
});

// Set encryption key
encryptedDb.run(`PRAGMA key = '${DB_ENCRYPTION_KEY}';`, (keyErr) => {
    if (keyErr) {
        console.error('Failed to set encryption key:', keyErr.message);
        process.exit(1);
    }
    console.log('Encryption key set');

    // Create unencrypted database and copy data
    encryptedDb.run(`ATTACH DATABASE '${UNENCRYPTED_DB_PATH}' AS unencrypted KEY '';`, (attachErr) => {
        if (attachErr) {
            console.error('Failed to attach unencrypted database:', attachErr.message);
            process.exit(1);
        }
        console.log('Attached unencrypted database');

        // Get list of tables
        encryptedDb.all("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';", (err, tables) => {
            if (err) {
                console.error('Failed to get table list:', err.message);
                process.exit(1);
            }

            console.log(`Found ${tables.length} tables to copy`);

            // Copy schema and data for each table
            let completed = 0;
            tables.forEach((table) => {
                const tableName = table.name;
                console.log(`Copying table: ${tableName}`);

                // Get table schema
                encryptedDb.get(`SELECT sql FROM sqlite_master WHERE type='table' AND name='${tableName}';`, (schemaErr, schemaRow) => {
                    if (schemaErr) {
                        console.error(`Failed to get schema for ${tableName}:`, schemaErr.message);
                        return;
                    }

                    // Create table in unencrypted database
                    const createTableSql = schemaRow.sql.replace(`CREATE TABLE ${tableName}`, `CREATE TABLE unencrypted.${tableName}`);
                    encryptedDb.run(createTableSql, (createErr) => {
                        if (createErr) {
                            console.error(`Failed to create table ${tableName}:`, createErr.message);
                            return;
                        }

                        // Copy data
                        encryptedDb.run(`INSERT INTO unencrypted.${tableName} SELECT * FROM ${tableName};`, (copyErr) => {
                            if (copyErr) {
                                console.error(`Failed to copy data for ${tableName}:`, copyErr.message);
                            } else {
                                console.log(`✅ Copied table: ${tableName}`);
                            }

                            completed++;
                            if (completed === tables.length) {
                                // Detach and close
                                encryptedDb.run('DETACH DATABASE unencrypted;', () => {
                                    encryptedDb.close((closeErr) => {
                                        if (closeErr) {
                                            console.error('Error closing database:', closeErr.message);
                                        } else {
                                            console.log('\n🎉 Export completed successfully!');
                                            console.log(`📁 Unencrypted database created at: ${UNENCRYPTED_DB_PATH}`);
                                            console.log('\n📋 You can now open this file with any SQLite GUI tool');
                                            console.log('⚠️  Remember to delete this unencrypted file after testing!');
                                            console.log('\n🗑️  To delete: node delete_unencrypted.js');
                                        }
                                    });
                                });
                            }
                        });
                    });
                });
            });
        });
    });
});
