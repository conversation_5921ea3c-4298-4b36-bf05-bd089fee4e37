<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7eef3] px-10 py-3">
          <div class="flex items-center gap-8">
            <div class="flex items-center gap-4 text-[#0e161b]">
              <div class="size-4">
                <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 6H42L36 24L42 42H6L12 24L6 6Z" fill="currentColor"></path></svg>
              </div>
              <h2 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em]">DentalCare</h2>
            </div>
            <div class="flex items-center gap-9">
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Patients</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Appointments</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Billing</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <label class="flex flex-col min-w-40 !h-10 max-w-64">
              <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                <div
                  class="text-[#4e7a97] flex border-none bg-[#e7eef3] items-center justify-center pl-4 rounded-l-xl border-r-0"
                  data-icon="MagnifyingGlass"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                    <path
                      d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                    ></path>
                  </svg>
                </div>
                <input
                  placeholder="Search"
                  class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0e161b] focus:outline-0 focus:ring-0 border-none bg-[#e7eef3] focus:border-none h-full placeholder:text-[#4e7a97] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  value=""
                />
              </div>
            </label>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAUkruB2lglzcxWyGXsXiAin-YhTp7spFDl9-2yN-kKUbPeLjaWWvonTvKqXxm5nAHJuxHjofpzwXhHgCptEc99eB0cnjoLuuSkrM3Qllv05SGh0XT2ZEx2lfEeB8mgvGTBLdkrKtp3rmI00JgjDflHY10gE-MR-3ECSgzNlxHX7CxLYWFy2LHBXcuGPDAHN8cn0vuh-r5YUprI9tHhoP7ZUlTQwRn1HjbmKW_zapMn9qP-DVzNZjec-Bv4aIN-Dew-qJM_8rtrsA");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <p class="text-[#0e161b] tracking-light text-[32px] font-bold leading-tight min-w-72">Patients</p>
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal"
              >
                <span class="truncate">Add Patient</span>
              </button>
            </div>
            <div class="px-4 py-3">
              <label class="flex flex-col min-w-40 h-12 w-full">
                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                  <div
                    class="text-[#4e7a97] flex border-none bg-[#e7eef3] items-center justify-center pl-4 rounded-l-xl border-r-0"
                    data-icon="MagnifyingGlass"
                    data-size="24px"
                    data-weight="regular"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                      <path
                        d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                      ></path>
                    </svg>
                  </div>
                  <input
                    placeholder="Search patients"
                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#0e161b] focus:outline-0 focus:ring-0 border-none bg-[#e7eef3] focus:border-none h-full placeholder:text-[#4e7a97] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                    value=""
                  />
                </div>
              </label>
            </div>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d0dee7] bg-slate-50">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-slate-50">
                      <th class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">
                        First Name
                      </th>
                      <th class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">
                        Last Name
                      </th>
                      <th class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">PESEL</th>
                      <th class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Gender</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Sophia</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Clark</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        ***********
                      </td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Female</td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Ethan</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Carter</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        90051567890
                      </td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Male</td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Olivia</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Bennett</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        78112223456
                      </td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Female</td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Liam</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Harper</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        82070834567
                      </td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Male</td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Ava</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Foster</td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        95091045678
                      </td>
                      <td class="table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Female</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-120{display: none;}}
                @container(max-width:240px){.table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-240{display: none;}}
                @container(max-width:360px){.table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-360{display: none;}}
                @container(max-width:480px){.table-7450b30a-3c86-410c-b6a3-d7d39d9e85c1-column-480{display: none;}}
              </style>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
