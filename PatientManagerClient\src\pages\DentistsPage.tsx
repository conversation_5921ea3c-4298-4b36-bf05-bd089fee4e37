import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  LocalHospital,
  Badge,
} from '@mui/icons-material';
import { Dentist } from '../services/localDatabase';
import { dentistService } from '../services/api';

const DentistsPage: React.FC = () => {
  const [dentists, setDentists] = useState<Dentist[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingDentist, setEditingDentist] = useState<Dentist | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Form state
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    license_number: '',
  });

  useEffect(() => {
    loadDentists();
  }, []);

  const loadDentists = async () => {
    try {
      setLoading(true);
      const data = await dentistService.getAll();
      setDentists(data);
    } catch (error) {
      console.error('Error loading dentists:', error);
      setSnackbar({ open: true, message: 'Error loading dentists', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (dentist?: Dentist) => {
    if (dentist) {
      setEditingDentist(dentist);
      setFormData({
        first_name: dentist.first_name,
        last_name: dentist.last_name,
        license_number: dentist.license_number,
      });
    } else {
      setEditingDentist(null);
      setFormData({
        first_name: '',
        last_name: '',
        license_number: '',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingDentist(null);
  };

  const handleSubmit = async () => {
    try {
      if (editingDentist) {
        await dentistService.update(editingDentist.id, formData);
        setSnackbar({ open: true, message: 'Dentist updated successfully', severity: 'success' });
      } else {
        await dentistService.create(formData);
        setSnackbar({ open: true, message: 'Dentist created successfully', severity: 'success' });
      }
      handleCloseDialog();
      loadDentists();
    } catch (error: any) {
      console.error('Error saving dentist:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Error saving dentist',
        severity: 'error'
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this dentist?')) {
      try {
        await dentistService.delete(id);
        setSnackbar({ open: true, message: 'Dentist deleted successfully', severity: 'success' });
        loadDentists();
      } catch (error: any) {
        console.error('Error deleting dentist:', error);
        setSnackbar({
          open: true,
          message: error.response?.data?.message || 'Error deleting dentist',
          severity: 'error'
        });
      }
    }
  };

  const filteredDentists = dentists.filter(dentist => {
    const searchLower = searchTerm.toLowerCase();
    const fullName = `${dentist.first_name} ${dentist.last_name}`;
    return (
      fullName.toLowerCase().includes(searchLower) ||
      dentist.license_number.toLowerCase().includes(searchLower)
    );
  });

  return (
    <Box sx={{ p: 3, maxWidth: '1200px', mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h1" sx={{ mb: 1 }}>
          Dentists
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage dental practitioners and their credentials
        </Typography>
      </Box>

      {/* Actions Bar */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search dentists..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1, maxWidth: '400px' }}
        />
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ borderRadius: '9999px' }}
        >
          Add Dentist
        </Button>
      </Box>

      {/* Dentists Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Dentist</TableCell>
                <TableCell>License Number</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">Loading...</TableCell>
                </TableRow>
              ) : filteredDentists.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    {searchTerm ? 'No dentists found matching your search' : 'No dentists found'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredDentists.map((dentist) => (
                  <TableRow key={dentist.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <LocalHospital color="primary" />
                        <Box>
                          <Typography variant="body1" fontWeight={600}>
                            Dr. {dentist.first_name} {dentist.last_name}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Badge color="action" />
                        <Typography variant="body2" fontFamily="monospace">
                          {dentist.license_number}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {dentist.created_at ? new Date(dentist.created_at).toLocaleDateString() : 'N/A'}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        onClick={() => handleOpenDialog(dentist)}
                        size="small"
                        color="primary"
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(dentist.id)}
                        size="small"
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Dentist Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '0.75rem' }
        }}
      >
        <DialogTitle>
          {editingDentist ? 'Edit Dentist' : 'Add New Dentist'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                required
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                required
              />
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="License Number"
                value={formData.license_number}
                onChange={(e) => setFormData({ ...formData, license_number: e.target.value })}
                required
                helperText="Professional license or certification number"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.first_name || !formData.last_name || !formData.license_number}
          >
            {editingDentist ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DentistsPage;
