import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  Add,
  Event,
  Person,
  AccessTime,
} from '@mui/icons-material';

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Mock appointments data
  const appointments = [
    {
      id: 1,
      time: '9:00 AM',
      patient: '<PERSON>',
      procedure: 'Routine Checkup',
      dentist: 'Dr. <PERSON>',
      status: 'Scheduled',
    },
    {
      id: 2,
      time: '10:30 AM',
      patient: '<PERSON>',
      procedure: 'Teeth Cleaning',
      dentist: 'Dr. <PERSON>',
      status: 'Confirmed',
    },
    {
      id: 3,
      time: '1:00 PM',
      patient: '<PERSON>',
      procedure: 'Filling',
      dentist: 'Dr. <PERSON>',
      status: 'Scheduled',
    },
    {
      id: 4,
      time: '2:30 PM',
      patient: '<PERSON>',
      procedure: 'Root Canal',
      dentist: 'Dr. <PERSON>',
      status: 'Confirmed',
    },
    {
      id: 5,
      time: '4:00 PM',
      patient: '<PERSON> <PERSON>',
      procedure: 'Braces Consultation',
      dentist: 'Dr. Emily Carter',
      status: 'Scheduled',
    },
  ];

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const daysOfWeek = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const isToday = (date: Date | null) => {
    if (!date) return false;
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date | null) => {
    if (!date || !selectedDate) return false;
    return date.toDateString() === selectedDate.toDateString();
  };

  const days = getDaysInMonth(currentDate);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
          Calendar
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          sx={{ borderRadius: '9999px' }}
        >
          New Appointment
        </Button>
      </Box>

      {/* Calendar Grid */}
      <Grid container spacing={4}>
        {/* Calendar */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            {/* Month Navigation */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <IconButton onClick={() => navigateMonth('prev')}>
                <ChevronLeft />
              </IconButton>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </Typography>
              <IconButton onClick={() => navigateMonth('next')}>
                <ChevronRight />
              </IconButton>
            </Box>

            {/* Days of Week Header */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1, mb: 1 }}>
              {daysOfWeek.map((day) => (
                <Box key={day} sx={{ textAlign: 'center', py: 1 }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                    {day}
                  </Typography>
                </Box>
              ))}
            </Box>

            {/* Calendar Days */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 1 }}>
              {days.map((date, index) => (
                <Box key={index} sx={{ textAlign: 'center' }}>
                  {date && (
                    <Button
                      onClick={() => setSelectedDate(date)}
                      sx={{
                        width: '40px',
                        height: '40px',
                        minWidth: '40px',
                        borderRadius: '50%',
                        color: isToday(date) ? 'white' : 'text.primary',
                        backgroundColor: isToday(date)
                          ? 'primary.main'
                          : isSelected(date)
                            ? 'action.selected'
                            : 'transparent',
                        '&:hover': {
                          backgroundColor: isToday(date)
                            ? 'primary.dark'
                            : 'action.hover',
                        },
                      }}
                    >
                      {date.getDate()}
                    </Button>
                  )}
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>

        {/* Next Month Preview */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center' }}>
              {monthNames[(currentDate.getMonth() + 1) % 12]} {currentDate.getMonth() === 11 ? currentDate.getFullYear() + 1 : currentDate.getFullYear()}
            </Typography>

            {/* Simplified next month view */}
            <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
              <Typography variant="body2">
                Next month preview
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Upcoming Appointments */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 3, color: 'text.primary' }}>
          Upcoming Appointments
        </Typography>

        <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: 'background.default' }}>
                <TableCell sx={{ fontWeight: 'bold' }}>Time</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Patient</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Procedure</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Dentist</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {appointments.map((appointment) => (
                <TableRow key={appointment.id} sx={{ '&:hover': { backgroundColor: 'action.hover' } }}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTime sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {appointment.time}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Person sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {appointment.patient}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {appointment.procedure}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {appointment.dentist}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={appointment.status}
                      size="small"
                      sx={{
                        backgroundColor: 'secondary.main',
                        color: 'secondary.contrastText',
                        borderRadius: '9999px',
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="text"
                      size="small"
                      sx={{ color: 'text.secondary', fontWeight: 'bold' }}
                    >
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </Box>
  );
};

export default CalendarPage;
