<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
      <div class="layout-container flex h-full grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#e7eef3] px-10 py-3">
          <div class="flex items-center gap-4 text-[#0e161b]">
            <div class="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M36.7273 44C33.9891 44 31.6043 39.8386 30.3636 33.69C29.123 39.8386 26.7382 44 24 44C21.2618 44 18.877 39.8386 17.6364 33.69C16.3957 39.8386 14.0109 44 11.2727 44C7.25611 44 4 35.0457 4 24C4 12.9543 7.25611 4 11.2727 4C14.0109 4 16.3957 8.16144 17.6364 14.31C18.877 8.16144 21.2618 4 24 4C26.7382 4 29.123 8.16144 30.3636 14.31C31.6043 8.16144 33.9891 4 36.7273 4C40.7439 4 44 12.9543 44 24C44 35.0457 40.7439 44 36.7273 44Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2 class="text-[#0e161b] text-lg font-bold leading-tight tracking-[-0.015em]">Dental Office</h2>
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Dashboard</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Patients</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Calendar</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Billing</a>
              <a class="text-[#0e161b] text-sm font-medium leading-normal" href="#">Reports</a>
            </div>
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 bg-[#e7eef3] text-[#0e161b] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
            >
              <div class="text-[#0e161b]" data-icon="Bell" data-size="20px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDJWxQt6jUZ01js99k8UK4Zqu2DMmb0DM7-z8kSlWF1ofb9VBqq3PVHSbb3-cB0JOIKkKqwvzhpBld7Jzo1r555doGWXKGvilAJFbvowfQSm-esXFf2s8r_EE49GXQtvZYJ-xGwyfKQMt9MNDHurRv6tm2IcsRH9UXb72yw8zpqKSiw5YpxTvxX-m3unE5MM9tMn-_q7kXiyQErFgnzsaOvj6RryiZfC-XZA-94y6VUFERr2R07_3FLB_j-DGlMYh0p7Ng3pmW2TQ");'
            ></div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="flex flex-wrap justify-between gap-3 p-4">
              <p class="text-[#0e161b] tracking-light text-[32px] font-bold leading-tight min-w-72">Calendar</p>
              <button
                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal"
              >
                <span class="truncate">New Appointment</span>
              </button>
            </div>
            <div class="pb-3">
              <div class="flex border-b border-[#d0dee7] px-4 gap-8">
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#4e7a97] pb-[13px] pt-4" href="#">
                  <p class="text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">Day</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-transparent text-[#4e7a97] pb-[13px] pt-4" href="#">
                  <p class="text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">Week</p>
                </a>
                <a class="flex flex-col items-center justify-center border-b-[3px] border-b-[#1993e5] text-[#0e161b] pb-[13px] pt-4" href="#">
                  <p class="text-[#0e161b] text-sm font-bold leading-normal tracking-[0.015em]">Month</p>
                </a>
              </div>
            </div>
            <div class="flex flex-wrap items-center justify-center gap-6 p-4">
              <div class="flex min-w-72 max-w-[336px] flex-1 flex-col gap-0.5">
                <div class="flex items-center p-1 justify-between">
                  <button>
                    <div class="text-[#0e161b] flex size-10 items-center justify-center" data-icon="CaretLeft" data-size="18px" data-weight="regular">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z"></path>
                      </svg>
                    </div>
                  </button>
                  <p class="text-[#0e161b] text-base font-bold leading-tight flex-1 text-center pr-10">October 2024</p>
                </div>
                <div class="grid grid-cols-7">
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">S</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">M</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">T</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">W</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">T</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">F</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">S</p>
                  <button class="h-12 w-full text-[#0e161b] col-start-4 text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">1</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">2</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">3</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">4</div>
                  </button>
                  <button class="h-12 w-full text-slate-50 text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full bg-[#1993e5]">5</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">6</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">7</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">8</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">9</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">10</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">11</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">12</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">13</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">14</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">15</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">16</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">17</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">18</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">19</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">20</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">21</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">22</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">23</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">24</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">25</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">26</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">27</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">28</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">29</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">30</div>
                  </button>
                </div>
              </div>
              <div class="flex min-w-72 max-w-[336px] flex-1 flex-col gap-0.5">
                <div class="flex items-center p-1 justify-between">
                  <p class="text-[#0e161b] text-base font-bold leading-tight flex-1 text-center pl-10">November 2024</p>
                  <button>
                    <div class="text-[#0e161b] flex size-10 items-center justify-center" data-icon="CaretRight" data-size="18px" data-weight="regular">
                      <svg xmlns="http://www.w3.org/2000/svg" width="18px" height="18px" fill="currentColor" viewBox="0 0 256 256">
                        <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
                      </svg>
                    </div>
                  </button>
                </div>
                <div class="grid grid-cols-7">
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">S</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">M</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">T</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">W</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">T</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">F</p>
                  <p class="text-[#0e161b] text-[13px] font-bold leading-normal tracking-[0.015em] flex h-12 w-full items-center justify-center pb-0.5">S</p>
                  <button class="h-12 w-full text-[#0e161b] col-start-4 text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">1</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">2</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">3</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">4</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">5</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">6</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">7</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">8</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">9</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">10</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">11</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">12</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">13</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">14</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">15</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">16</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">17</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">18</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">19</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">20</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">21</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">22</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">23</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">24</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">25</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">26</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">27</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">28</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">29</div>
                  </button>
                  <button class="h-12 w-full text-[#0e161b] text-sm font-medium leading-normal">
                    <div class="flex size-full items-center justify-center rounded-full">30</div>
                  </button>
                </div>
              </div>
            </div>
            <h2 class="text-[#0e161b] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Upcoming Appointments</h2>
            <div class="px-4 py-3 @container">
              <div class="flex overflow-hidden rounded-xl border border-[#d0dee7] bg-slate-50">
                <table class="flex-1">
                  <thead>
                    <tr class="bg-slate-50">
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Time</th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Patient</th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">
                        Procedure
                      </th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">Dentist</th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 px-4 py-3 text-left text-[#0e161b] w-[400px] text-sm font-medium leading-normal">
                        Card Number
                      </th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 px-4 py-3 text-left text-[#0e161b] w-60 text-sm font-medium leading-normal">Status</th>
                      <th class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 px-4 py-3 text-left text-[#0e161b] w-60 text-[#4e7a97] text-sm font-medium leading-normal">
                        Visit Details
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">9:00 AM</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0e161b] text-sm font-normal leading-normal">
                        Sophia Clark
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Routine Checkup
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Emily Carter
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        1234567890123456
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Scheduled</span>
                        </button>
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 h-[72px] px-4 py-2 w-60 text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">10:30 AM</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0e161b] text-sm font-normal leading-normal">
                        Ethan Miller
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Teeth Cleaning
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Emily Carter
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        9876543210987654
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Confirmed</span>
                        </button>
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 h-[72px] px-4 py-2 w-60 text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">1:00 PM</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0e161b] text-sm font-normal leading-normal">
                        Olivia Davis
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">Filling</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Michael Chen
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        4567890123456789
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Scheduled</span>
                        </button>
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 h-[72px] px-4 py-2 w-60 text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">2:30 PM</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0e161b] text-sm font-normal leading-normal">
                        Noah Wilson
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Root Canal
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Michael Chen
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        6543210987654321
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Confirmed</span>
                        </button>
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 h-[72px] px-4 py-2 w-60 text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                    <tr class="border-t border-t-[#d0dee7]">
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-120 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">4:00 PM</td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-240 h-[72px] px-4 py-2 w-[400px] text-[#0e161b] text-sm font-normal leading-normal">
                        Ava Thompson
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-360 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Braces Consultation
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-480 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        Dr. Emily Carter
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-600 h-[72px] px-4 py-2 w-[400px] text-[#4e7a97] text-sm font-normal leading-normal">
                        7890123456789012
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-720 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <button
                          class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#e7eef3] text-[#0e161b] text-sm font-medium leading-normal w-full"
                        >
                          <span class="truncate">Scheduled</span>
                        </button>
                      </td>
                      <td class="table-f204ea55-8783-41af-bf30-4956748dd90a-column-840 h-[72px] px-4 py-2 w-60 text-[#4e7a97] text-sm font-bold leading-normal tracking-[0.015em]">
                        View
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <style>
                          @container(max-width:120px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-120{display: none;}}
                @container(max-width:240px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-240{display: none;}}
                @container(max-width:360px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-360{display: none;}}
                @container(max-width:480px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-480{display: none;}}
                @container(max-width:600px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-600{display: none;}}
                @container(max-width:720px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-720{display: none;}}
                @container(max-width:840px){.table-f204ea55-8783-41af-bf30-4956748dd90a-column-840{display: none;}}
              </style>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
