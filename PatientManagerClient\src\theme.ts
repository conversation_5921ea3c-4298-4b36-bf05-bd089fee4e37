import { createTheme, PaletteMode, ThemeOptions } from '@mui/material/styles';

// Define theme settings for both light and dark modes - Updated for dental office design
const getThemeOptions = (mode: PaletteMode): ThemeOptions => ({
  palette: {
    mode,
    ...(mode === 'light'
      ? {
          // Light mode palette - Exact colors from dental office HTML design
          primary: {
            main: '#1993e5', // Blue from design - used sparingly
            light: '#4fb3f4',
            dark: '#1976d2',
            contrastText: '#ffffff',
          },
          secondary: {
            main: '#e7eef3', // Very light gray-blue for buttons/backgrounds
            light: '#f1f5f9',
            dark: '#d0dee7',
            contrastText: '#0e161b',
          },
          background: {
            default: '#f8fafc', // slate-50 - main background
            paper: '#ffffff', // White for cards
          },
          error: {
            main: '#dc2626', // Muted red
          },
          warning: {
            main: '#d97706', // Muted amber
          },
          success: {
            main: '#059669', // Muted emerald
          },
          text: {
            primary: '#0e161b', // Very dark blue-black - primary text
            secondary: '#4e7a97', // Muted blue-gray - secondary text
            disabled: '#9ca3af', // Light gray for disabled
          },
          divider: '#d0dee7', // Very light blue-gray borders
          action: {
            hover: 'rgba(231, 238, 243, 0.5)', // Very subtle hover
            selected: 'rgba(231, 238, 243, 0.8)', // Very subtle selection
          },
        }
      : {
          // Dark mode palette
          primary: {
            main: '#3b82f6', // Blue 500
            light: '#60a5fa',
            dark: '#2563eb',
            contrastText: '#ffffff',
          },
          secondary: {
            main: '#64748b', // Slate 500
            light: '#94a3b8',
            dark: '#475569',
            contrastText: '#ffffff',
          },
          background: {
            default: '#0f172a', // slate-900
            paper: '#1e293b', // slate-800
          },
          error: {
            main: '#ef4444', // Red 500
          },
          warning: {
            main: '#f59e0b', // Amber 500
          },
          success: {
            main: '#10b981', // Emerald 500
          },
          text: {
            primary: '#f1f5f9', // slate-100
            secondary: '#94a3b8', // slate-400
            disabled: '#64748b', // slate-500
          },
          divider: '#334155', // slate-700
        }),
  },
  typography: {
    fontFamily: 'Inter, "Noto Sans", sans-serif', // Updated to match design
    fontSize: 14,
    h1: {
      fontSize: '2rem', // 32px
      fontWeight: 700,
      letterSpacing: '-0.015em',
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '1.375rem', // 22px
      fontWeight: 700,
      letterSpacing: '-0.015em',
      lineHeight: 1.2,
    },
    h3: {
      fontSize: '1.125rem', // 18px
      fontWeight: 700,
      letterSpacing: '-0.015em',
      lineHeight: 1.2,
    },
    h4: {
      fontSize: '1rem', // 16px
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h5: {
      fontSize: '0.875rem', // 14px
      fontWeight: 700,
      letterSpacing: '0.015em',
      lineHeight: 1.2,
    },
    h6: {
      fontSize: '0.8125rem', // 13px
      fontWeight: 700,
      letterSpacing: '0.015em',
      lineHeight: 1.2,
    },
    body1: {
      fontSize: '0.875rem', // 14px
      fontWeight: 400,
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.8125rem', // 13px
      fontWeight: 400,
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
      letterSpacing: '0.015em',
    },
  },
  shape: {
    borderRadius: 12, // Updated to match design (0.75rem)
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          borderRadius: '0.75rem', // 12px
          border: '1px solid',
          borderColor: mode === 'light' ? '#d0dee7' : '#334155',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: '9999px', // rounded-full like in HTML design
          fontWeight: 500,
          padding: '8px 16px',
          minHeight: '36px',
        },
        contained: {
          backgroundColor: mode === 'light' ? '#1993e5' : '#3b82f6', // Use primary blue sparingly
          color: '#ffffff',
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: mode === 'light' ? '#1976d2' : '#2563eb',
            boxShadow: 'none',
          },
        },
        outlined: {
          borderColor: mode === 'light' ? '#d0dee7' : '#334155',
          color: mode === 'light' ? '#0e161b' : '#f1f5f9',
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: mode === 'light' ? '#e7eef3' : '#1e293b',
            borderColor: mode === 'light' ? '#d0dee7' : '#334155',
          },
        },
        text: {
          color: mode === 'light' ? '#4e7a97' : '#94a3b8',
          '&:hover': {
            backgroundColor: mode === 'light' ? 'rgba(231, 238, 243, 0.5)' : 'rgba(148, 163, 184, 0.1)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '0.75rem', // 12px
          border: '1px solid',
          borderColor: mode === 'light' ? '#d0dee7' : '#334155',
          boxShadow: mode === 'light'
            ? '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
            : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '0.75rem', // 12px
            '& fieldset': {
              borderColor: mode === 'light' ? '#d0dee7' : '#334155',
            },
            '&:hover fieldset': {
              borderColor: mode === 'light' ? '#4e7a97' : '#64748b',
            },
            '&.Mui-focused fieldset': {
              borderColor: mode === 'light' ? '#1993e5' : '#3b82f6',
              borderWidth: '2px',
            },
          },
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: '0.75rem',
          border: '1px solid',
          borderColor: mode === 'light' ? '#d0dee7' : '#334155',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: mode === 'light' ? '#f8fafc' : '#1e293b',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          fontSize: '0.8125rem', // 13px
          letterSpacing: '0.015em',
          textTransform: 'uppercase',
          color: mode === 'light' ? '#4e7a97' : '#94a3b8',
        },
        body: {
          fontSize: '0.875rem', // 14px
          borderColor: mode === 'light' ? '#d0dee7' : '#334155',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '9999px', // rounded-full
          fontWeight: 500,
          fontSize: '0.8125rem', // 13px
        },
      },
    },
  },
});

// Create theme creator function
export const createAppTheme = (mode: PaletteMode) => {
  return createTheme(getThemeOptions(mode));
};

// Default theme (light mode to match dental office design)
const theme = createAppTheme('light');

export default theme;
