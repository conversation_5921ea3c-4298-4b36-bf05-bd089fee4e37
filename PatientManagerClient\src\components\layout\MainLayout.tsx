import React from 'react';
import { Box, Alert } from '@mui/material';
import { Outlet } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import Header from './Header';

const MainLayout: React.FC = () => {
  const { isOnline } = useAuth();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Header */}
      <Header />

      {/* Status bar for offline mode */}
      {!isOnline && (
        <Alert severity="warning" sx={{ mx: 3, mt: 2 }}>
          Server Offline: Read-Only, Data May Be Outdated
        </Alert>
      )}

      {/* Main content area */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Outlet />
      </Box>
    </Box>
  );
};

export default MainLayout;
