const sqlite3 = require('@journeyapps/sqlcipher').verbose();
const path = require('path');
const fs = require('fs');

const DB_PATH = path.join(__dirname, 'database/patientmanager_server.db');
const BACKUP_PATH = path.join(__dirname, 'database/patientmanager_server_backup.db');
const NEW_DB_PATH = path.join(__dirname, 'database/patientmanager_server_new.db');

const OLD_KEY = 'your_super_secret_and_strong_encryption_key_here';
const NEW_KEY = 'dental_practice_secure_key_2024_v1';

console.log('🔄 Migrating database to new encryption key...');

// Step 1: Create backup
if (fs.existsSync(DB_PATH)) {
    console.log('📋 Creating backup of current database...');
    fs.copyFileSync(DB_PATH, BACKUP_PATH);
    console.log('✅ Backup created');
}

// Step 2: Open old database with old key
console.log('🔓 Opening database with old key...');
const oldDb = new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
        console.error('❌ Failed to open old database:', err.message);
        process.exit(1);
    }
});

oldDb.run(`PRAGMA key = '${OLD_KEY}';`, (keyErr) => {
    if (keyErr) {
        console.error('❌ Failed to set old encryption key:', keyErr.message);
        process.exit(1);
    }
    console.log('🔑 Old encryption key set');

    // Step 3: Create new database with new key
    console.log('🆕 Creating new database with new key...');
    
    // Remove new database if it exists
    if (fs.existsSync(NEW_DB_PATH)) {
        fs.unlinkSync(NEW_DB_PATH);
    }

    oldDb.run(`ATTACH DATABASE '${NEW_DB_PATH}' AS new_db KEY '${NEW_KEY}';`, (attachErr) => {
        if (attachErr) {
            console.error('❌ Failed to attach new database:', attachErr.message);
            process.exit(1);
        }
        console.log('🔗 New database attached');

        // Step 4: Get all tables and copy them
        oldDb.all("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';", (err, tables) => {
            if (err) {
                console.error('❌ Failed to get table list:', err.message);
                process.exit(1);
            }

            console.log(`📊 Found ${tables.length} tables to migrate`);

            let completed = 0;
            const totalTables = tables.length;

            if (totalTables === 0) {
                console.log('ℹ️  No tables found - creating fresh database');
                finalizeMigration();
                return;
            }

            tables.forEach((table) => {
                const tableName = table.name;
                console.log(`📋 Migrating table: ${tableName}`);

                // Get table schema
                oldDb.get(`SELECT sql FROM sqlite_master WHERE type='table' AND name='${tableName}';`, (schemaErr, schemaRow) => {
                    if (schemaErr) {
                        console.error(`❌ Failed to get schema for ${tableName}:`, schemaErr.message);
                        return;
                    }

                    // Create table in new database
                    const createTableSql = schemaRow.sql.replace(`CREATE TABLE ${tableName}`, `CREATE TABLE new_db.${tableName}`);
                    oldDb.run(createTableSql, (createErr) => {
                        if (createErr) {
                            console.error(`❌ Failed to create table ${tableName}:`, createErr.message);
                            return;
                        }

                        // Copy data
                        oldDb.run(`INSERT INTO new_db.${tableName} SELECT * FROM ${tableName};`, (copyErr) => {
                            if (copyErr) {
                                console.error(`❌ Failed to copy data for ${tableName}:`, copyErr.message);
                            } else {
                                console.log(`✅ Migrated table: ${tableName}`);
                            }

                            completed++;
                            if (completed === totalTables) {
                                finalizeMigration();
                            }
                        });
                    });
                });
            });
        });
    });
});

function finalizeMigration() {
    console.log('🔄 Finalizing migration...');
    
    // Detach new database
    oldDb.run('DETACH DATABASE new_db;', (detachErr) => {
        if (detachErr) {
            console.error('❌ Failed to detach new database:', detachErr.message);
        }

        // Close old database
        oldDb.close((closeErr) => {
            if (closeErr) {
                console.error('❌ Error closing old database:', closeErr.message);
            }

            // Replace old database with new one
            try {
                if (fs.existsSync(NEW_DB_PATH)) {
                    fs.renameSync(DB_PATH, path.join(__dirname, 'database/patientmanager_server_old.db'));
                    fs.renameSync(NEW_DB_PATH, DB_PATH);
                    
                    console.log('🎉 Migration completed successfully!');
                    console.log('📁 New database is now active');
                    console.log('💾 Old database backed up as patientmanager_server_old.db');
                    console.log('🔄 Please restart your server now');
                } else {
                    console.log('ℹ️  No data to migrate - fresh database will be created on server start');
                }
            } catch (replaceErr) {
                console.error('❌ Failed to replace database files:', replaceErr.message);
                console.log('🔧 Manual intervention required');
            }
        });
    });
}
