import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
// Import our bcrypt shim instead of the actual bcryptjs
import bcrypt from '../utils/bcryptShim';
import apiService from '../services/api';
import localDatabaseService from '../services/localDatabase';

// Define the context type
interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  userId: number | null;
  username: string | null;
  user: { username: string } | null; // Add user property for Header component
  isOnline: boolean;
  userExistsOnServer: boolean;
  localUserExists: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  register: (username: string, password: string) => Promise<boolean>;
  checkServerStatus: () => Promise<boolean>;
  checkUserExists: () => Promise<boolean>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Props for the AuthProvider component
interface AuthProviderProps {
  children: ReactNode;
}

// AuthProvider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<number | null>(null);
  const [username, setUsername] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState<boolean>(false);
  const [userExistsOnServer, setUserExistsOnServer] = useState<boolean>(false);
  const [localUserExists, setLocalUserExists] = useState<boolean>(false);

  // Check server status periodically
  useEffect(() => {
    const checkStatus = async () => {
      try {
        console.log('Periodic server status check...');
        const online = await apiService.checkServerStatus();
        console.log('Periodic check result:', online);
        setIsOnline(online);
      } catch (error) {
        console.error('Error in periodic server status check:', error);
        setIsOnline(false);
      }
    };

    // Check immediately on mount
    checkStatus();

    // Then check every 15 seconds (more frequent checks for better responsiveness)
    const interval = setInterval(checkStatus, 15000);

    // Clean up on unmount
    return () => clearInterval(interval);
  }, []);

  // Check for existing token on mount
  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);

      try {
        // Get the registered username and local user credentials
        const registeredUsername = localStorage.getItem('registeredUsername');
        const localUserStr = localStorage.getItem('localUser');
        const localUser = localUserStr ? JSON.parse(localUserStr) : null;

        // Check if local user exists
        const hasLocalUser = !!localUser;
        setLocalUserExists(hasLocalUser);

        console.log('Registered username:', registeredUsername || 'None');
        console.log('Local user exists:', hasLocalUser ? 'Yes' : 'No');

        // Clear any existing token to ensure user must log in again
        apiService.clearToken();

        // Check if server is online
        const online = await apiService.checkServerStatus();
        console.log('Server online during init:', online);
        setIsOnline(online);

        // If server is online, check if user exists on server
        if (online) {
          const serverUserExists = await apiService.checkUserExists();
          setUserExistsOnServer(serverUserExists);
          console.log('User exists on server:', serverUserExists);
        }

        // Set username from local storage if available (for display only)
        if (registeredUsername) {
          setUsername(registeredUsername);
          console.log('Username set from local storage for display');
        }

        // Always require login - never start authenticated
        setIsAuthenticated(false);
        setUserId(null);

      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear any invalid tokens
        apiService.clearToken();
        setIsAuthenticated(false);
        setUserId(null);
        setUsername(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    try {
      // Check if server is online
      console.log('Checking server status...');
      const online = await apiService.checkServerStatus();
      console.log('Server online status:', online);
      setIsOnline(online);

      // Get the local user credentials if they exist
      const localUserStr = localStorage.getItem('localUser');
      const localUser = localUserStr ? JSON.parse(localUserStr) : null;
      setLocalUserExists(!!localUser);

      // CASE 1: Server is online - authenticate against server
      if (online) {
        console.log('Server is online, authenticating against server');

        try {
          // Online login - authenticate with server
          console.log('Attempting online login...');
          const response = await apiService.login(username, password);

          // If we get here, authentication was successful (otherwise an error would be thrown)
          console.log('Online login successful, token received');
          setIsAuthenticated(true);
          setUserId(response.userId);
          setUsername(username);

          // Store the username for future logins
          localStorage.setItem('registeredUsername', username);

          // Store local credentials for offline authentication
          const salt = bcrypt.genSaltSync(10);
          const passwordHash = bcrypt.hashSync(password, salt);

          localStorage.setItem('localUser', JSON.stringify({
            username,
            passwordHash,
            salt
          }));
          setLocalUserExists(true);

          console.log('Local user credentials created/updated for offline authentication');

          // Initialize local database with password for encryption
          await localDatabaseService.initialize(password);
          console.log('Local database initialized with password for encryption');

          // Sync local database with server data
          try {
            console.log('Syncing local database with server data...');
            const patients = await apiService.getPatients();
            const procedures = await apiService.getProcedures();
            const appointments = await apiService.getAppointments();

            // We don't have an endpoint for attachments, so use an empty array
            await localDatabaseService.syncWithServer(
              patients,
              procedures,
              appointments,
              []
            );
            console.log('Local database synced with server data');
          } catch (syncError) {
            console.error('Error syncing local database:', syncError);
            // Continue anyway, as this is not critical
          }

          return true;
        } catch (loginError) {
          console.error('Online login failed:', loginError);

          // Clear authentication state
          setIsAuthenticated(false);
          setUserId(null);

          // Keep username for display purposes only

          return false;
        }
      }
      // CASE 2: Server is offline and local user exists - authenticate against local credentials
      else if (!online && localUser) {
        console.log('Server is offline, authenticating against local credentials');

        // Verify the password against the stored hash
        const passwordMatch = bcrypt.compareSync(password, localUser.passwordHash);

        if (passwordMatch) {
          console.log('Local authentication successful');

          // Create a mock token for offline mode
          const mockToken = 'offline_token_' + Date.now();
          apiService.setToken(mockToken);

          setIsAuthenticated(true);
          setUserId(1);
          setUsername(localUser.username);

          // Initialize local database with password for encryption
          await localDatabaseService.initialize(password);
          console.log('Local database initialized with password for encryption');

          return true;
        } else {
          console.log('Local authentication failed: password does not match');

          // Clear authentication state
          setIsAuthenticated(false);
          setUserId(null);

          return false;
        }
      }
      // CASE 3: Server is offline and no local user - cannot authenticate
      else {
        console.log('Server is offline and no local user exists - cannot authenticate');

        // Clear authentication state
        setIsAuthenticated(false);
        setUserId(null);

        return false;
      }
    } catch (error) {
      console.error('Login error:', error);

      // Clear authentication state
      setIsAuthenticated(false);
      setUserId(null);

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function for offline login
  const handleOfflineLogin = async (username: string, password: string): Promise<boolean> => {
    console.log('Handling offline login...');

    // Create a mock token for offline mode
    const mockToken = 'offline_token_' + Date.now();
    apiService.setToken(mockToken);

    setIsAuthenticated(true);
    setUserId(1);
    setUsername(username);
    setIsOnline(false); // Ensure we're in offline mode

    try {
      // Initialize local database with password for encryption
      await localDatabaseService.initialize(password);
      console.log('Local database initialized with password for encryption');
      return true;
    } catch (dbError) {
      console.error('Failed to initialize local database:', dbError);
      return false;
    }
  };

  // Logout function
  const logout = () => {
    // Clear token from API service
    apiService.clearToken();

    // Close local database connection
    localDatabaseService.close();

    // Clear authentication state
    setIsAuthenticated(false);
    setUserId(null);

    // Keep username for display purposes only
    // Don't clear setUsername(null) so the login form can still show the username

    console.log('User logged out successfully');
  };

  // Register function
  const register = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);

    try {
      // Check if server is online
      console.log('Checking server status for registration...');
      const online = await apiService.checkServerStatus();
      console.log('Server online status for registration:', online);
      setIsOnline(online);

      // Registration is only allowed when server is online
      if (!online) {
        console.error('Cannot register when offline');
        return false;
      }

      try {
        // Register with server
        console.log('Attempting to register with server...');
        const response = await apiService.register(username, password);

        if (response && response.userId) {
          console.log('Registration successful');

          // Store the password hash locally for offline authentication
          const salt = bcrypt.genSaltSync(10);
          const passwordHash = bcrypt.hashSync(password, salt);

          // Store user credentials for offline authentication
          localStorage.setItem('localUser', JSON.stringify({
            username,
            passwordHash,
            salt
          }));
          setLocalUserExists(true);

          console.log('Local user credentials stored for offline authentication');

          // Store the registered username for future logins
          localStorage.setItem('registeredUsername', username);

          // Set user as authenticated
          setIsAuthenticated(true);
          setUserId(response.userId);
          setUsername(username);

          // Update server user exists flag
          setUserExistsOnServer(true);

          // Initialize local database with password for encryption
          await localDatabaseService.initialize(password);
          console.log('Local database initialized with password for encryption');

          // Sync local database with server data
          try {
            console.log('Syncing local database with server data after registration...');
            const patients = await apiService.getPatients();
            const procedures = await apiService.getProcedures();
            const appointments = await apiService.getAppointments();

            // We don't have an endpoint for attachments, so use an empty array
            await localDatabaseService.syncWithServer(
              patients,
              procedures,
              appointments,
              []
            );
            console.log('Local database synced with server data');
          } catch (syncError) {
            console.error('Error syncing local database:', syncError);
            // Continue anyway, as this is not critical
          }

          return true;
        } else {
          console.error('Registration response missing userId');
          return false;
        }
      } catch (registerError) {
        console.error('Server registration failed:', registerError);
        return false;
      }
    } catch (error) {
      console.error('Register error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Check server status function
  const checkServerStatus = async (): Promise<boolean> => {
    try {
      console.log('Checking server status from AuthContext...');
      const online = await apiService.checkServerStatus();
      console.log('Server status result:', online);
      setIsOnline(online);

      // If server is online, check if user exists
      if (online) {
        await checkUserExists();
      }

      return online;
    } catch (error) {
      console.error('Error checking server status:', error);
      setIsOnline(false);
      return false;
    }
  };

  // Check if user exists on server
  const checkUserExists = async (): Promise<boolean> => {
    try {
      if (!isOnline) {
        console.log('Cannot check if user exists: server is offline');
        return false;
      }

      const exists = await apiService.checkUserExists();
      console.log('User exists on server:', exists);
      setUserExistsOnServer(exists);
      return exists;
    } catch (error) {
      console.error('Error checking if user exists:', error);
      return false;
    }
  };

  // Context value
  const value = {
    isAuthenticated,
    isLoading,
    userId,
    username,
    user: username ? { username } : null, // Create user object from username
    isOnline,
    userExistsOnServer,
    localUserExists,
    login,
    logout,
    register,
    checkServerStatus,
    checkUserExists,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
