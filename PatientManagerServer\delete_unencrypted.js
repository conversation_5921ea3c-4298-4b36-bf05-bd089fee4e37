const fs = require('fs');
const path = require('path');

const UNENCRYPTED_DB_PATH = path.join(__dirname, 'database/patientmanager_unencrypted.db');

console.log('Deleting unencrypted database file...');

if (fs.existsSync(UNENCRYPTED_DB_PATH)) {
    try {
        fs.unlinkSync(UNENCRYPTED_DB_PATH);
        console.log('✅ Unencrypted database file deleted successfully');
        console.log('🔐 Your data is now secure again');
    } catch (error) {
        console.error('❌ Failed to delete unencrypted database:', error.message);
    }
} else {
    console.log('ℹ️  No unencrypted database file found');
}
