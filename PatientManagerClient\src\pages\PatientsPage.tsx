import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  Person,
  Phone,
  Email,
  LocationOn,
  Work,
  Badge,
} from '@mui/icons-material';
import { Patient } from '../services/localDatabase';
import { patientService } from '../services/api';

const PatientsPage: React.FC = () => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingPatient, setEditingPatient] = useState<Patient | null>(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Form state
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    pesel: '',
    date_of_birth: '',
    address: '',
    occupation: '',
    gender: '' as 'M' | 'K' | '',
    contact_information: '',
  });

  useEffect(() => {
    loadPatients();
  }, []);

  const loadPatients = async () => {
    try {
      setLoading(true);
      const data = await patientService.getAll();
      setPatients(data);
    } catch (error) {
      console.error('Error loading patients:', error);
      setSnackbar({ open: true, message: 'Error loading patients', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (patient?: Patient) => {
    if (patient) {
      setEditingPatient(patient);
      setFormData({
        first_name: patient.first_name || '',
        last_name: patient.last_name || '',
        pesel: patient.pesel || '',
        date_of_birth: patient.date_of_birth || '',
        address: patient.address || '',
        occupation: patient.occupation || '',
        gender: patient.gender || '',
        contact_information: patient.contact_information || '',
      });
    } else {
      setEditingPatient(null);
      setFormData({
        first_name: '',
        last_name: '',
        pesel: '',
        date_of_birth: '',
        address: '',
        occupation: '',
        gender: '',
        contact_information: '',
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingPatient(null);
  };

  const handleSubmit = async () => {
    try {
      if (editingPatient) {
        await patientService.update(editingPatient.id, formData);
        setSnackbar({ open: true, message: 'Patient updated successfully', severity: 'success' });
      } else {
        await patientService.create(formData);
        setSnackbar({ open: true, message: 'Patient created successfully', severity: 'success' });
      }
      handleCloseDialog();
      loadPatients();
    } catch (error: any) {
      console.error('Error saving patient:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Error saving patient',
        severity: 'error'
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this patient?')) {
      try {
        await patientService.delete(id);
        setSnackbar({ open: true, message: 'Patient deleted successfully', severity: 'success' });
        loadPatients();
      } catch (error) {
        console.error('Error deleting patient:', error);
        setSnackbar({ open: true, message: 'Error deleting patient', severity: 'error' });
      }
    }
  };

  const filteredPatients = patients.filter(patient => {
    const searchLower = searchTerm.toLowerCase();
    const fullName = `${patient.first_name || ''} ${patient.last_name || ''}`.trim() || patient.name || '';
    return (
      fullName.toLowerCase().includes(searchLower) ||
      (patient.pesel && patient.pesel.toLowerCase().includes(searchLower)) ||
      (patient.contact_information && patient.contact_information.toLowerCase().includes(searchLower))
    );
  });

  const getPatientDisplayName = (patient: Patient) => {
    if (patient.first_name && patient.last_name) {
      return `${patient.first_name} ${patient.last_name}`;
    }
    return patient.name || 'Unknown Patient';
  };

  return (
    <Box sx={{ p: 3, maxWidth: '1200px', mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h1" sx={{ mb: 1 }}>
          Patients
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage patient records and information
        </Typography>
      </Box>

      {/* Actions Bar */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search patients..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1, maxWidth: '400px' }}
        />
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ borderRadius: '9999px' }}
        >
          Add Patient
        </Button>
      </Box>

      {/* Patients Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Patient</TableCell>
                <TableCell>PESEL</TableCell>
                <TableCell>Date of Birth</TableCell>
                <TableCell>Gender</TableCell>
                <TableCell>Contact</TableCell>
                <TableCell>Address</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">Loading...</TableCell>
                </TableRow>
              ) : filteredPatients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    {searchTerm ? 'No patients found matching your search' : 'No patients found'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredPatients.map((patient) => (
                  <TableRow key={patient.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Person color="action" />
                        <Box>
                          <Typography variant="body1" fontWeight={600}>
                            {getPatientDisplayName(patient)}
                          </Typography>
                          {patient.occupation && (
                            <Typography variant="body2" color="text.secondary">
                              {patient.occupation}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {patient.pesel ? (
                        <Chip
                          label={patient.pesel}
                          size="small"
                          variant="outlined"
                          icon={<Badge />}
                        />
                      ) : (
                        <Typography variant="body2" color="text.disabled">
                          Not provided
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {patient.date_of_birth || 'Not provided'}
                    </TableCell>
                    <TableCell>
                      {patient.gender ? (
                        <Chip
                          label={patient.gender === 'M' ? 'Male' : 'Female'}
                          size="small"
                          color={patient.gender === 'M' ? 'primary' : 'secondary'}
                        />
                      ) : (
                        'Not specified'
                      )}
                    </TableCell>
                    <TableCell>
                      {patient.contact_information || 'Not provided'}
                    </TableCell>
                    <TableCell>
                      {patient.address || 'Not provided'}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        onClick={() => handleOpenDialog(patient)}
                        size="small"
                        color="primary"
                      >
                        <Edit />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(patient.id)}
                        size="small"
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* Patient Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '0.75rem' }
        }}
      >
        <DialogTitle>
          {editingPatient ? 'Edit Patient' : 'Add New Patient'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                required
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                required
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="PESEL"
                value={formData.pesel}
                onChange={(e) => setFormData({ ...formData, pesel: e.target.value })}
                helperText="Polish national identification number"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Date of Birth"
                type="date"
                value={formData.date_of_birth}
                onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={formData.gender}
                  label="Gender"
                  onChange={(e) => setFormData({ ...formData, gender: e.target.value as 'M' | 'K' })}
                >
                  <MenuItem value="M">Male</MenuItem>
                  <MenuItem value="K">Female</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Occupation"
                value={formData.occupation}
                onChange={(e) => setFormData({ ...formData, occupation: e.target.value })}
              />
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
            <Grid size={12}>
              <TextField
                fullWidth
                label="Contact Information"
                value={formData.contact_information}
                onChange={(e) => setFormData({ ...formData, contact_information: e.target.value })}
                helperText="Phone number, email, etc."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!formData.first_name || !formData.last_name}
          >
            {editingPatient ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PatientsPage;
